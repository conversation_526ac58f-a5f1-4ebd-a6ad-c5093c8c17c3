#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت المتطلبات التلقائي
يقوم بتثبيت جميع المكتبات المطلوبة تلقائياً
"""

import sys
import subprocess
import os

def run_command(command):
    """تشغيل أمر في النظام مع معالجة الأخطاء"""
    try:
        print(f"🔄 تشغيل: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بنجاح")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ فشل: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الأمر: {e}")
        return False

def check_pip():
    """فحص وجود pip"""
    print("🔍 فحص pip...")
    try:
        import pip
        print("✅ pip متوفر")
        return True
    except ImportError:
        print("❌ pip غير متوفر")
        return False

def upgrade_pip():
    """ترقية pip لأحدث إصدار"""
    print("🔄 ترقية pip...")
    return run_command(f"{sys.executable} -m pip install --upgrade pip")

def install_requirements():
    """تثبيت المتطلبات من ملف requirements.txt"""
    print("📦 تثبيت المتطلبات...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    return run_command(f"{sys.executable} -m pip install -r requirements.txt")

def install_individual_packages():
    """تثبيت المكتبات الأساسية بشكل فردي في حالة فشل requirements.txt"""
    print("🔄 تثبيت المكتبات الأساسية بشكل فردي...")
    
    essential_packages = [
        "PyQt5>=5.15.0",
        "SQLAlchemy>=2.0.0", 
        "cryptography>=41.0.0",
        "openpyxl>=3.1.0",
        "reportlab>=4.0.0",
        "Pillow>=10.0.0"
    ]
    
    success_count = 0
    for package in essential_packages:
        print(f"\n📦 تثبيت {package}...")
        if run_command(f"{sys.executable} -m pip install {package}"):
            success_count += 1
        else:
            print(f"⚠️ فشل في تثبيت {package}")
    
    print(f"\n📊 تم تثبيت {success_count}/{len(essential_packages)} مكتبات")
    return success_count >= 4  # نحتاج على الأقل 4 مكتبات أساسية

def verify_installation():
    """التحقق من نجاح التثبيت"""
    print("\n🔍 التحقق من التثبيت...")
    
    test_imports = [
        ("PyQt5", "PyQt5.QtWidgets"),
        ("SQLAlchemy", "sqlalchemy"),
        ("التشفير", "cryptography"),
        ("Excel", "openpyxl"),
        ("PDF", "reportlab"),
        ("الصور", "PIL")
    ]
    
    success_count = 0
    for name, module in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}: متوفر")
            success_count += 1
        except ImportError:
            print(f"❌ {name}: غير متوفر")
    
    print(f"\n📊 {success_count}/{len(test_imports)} مكتبات متوفرة")
    return success_count >= 4

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 سكريبت تثبيت متطلبات البرنامج")
    print("=" * 60)
    
    # فحص pip
    if not check_pip():
        print("❌ يجب تثبيت pip أولاً")
        return False
    
    # ترقية pip
    print("\n" + "─" * 40)
    upgrade_pip()
    
    # تثبيت المتطلبات
    print("\n" + "─" * 40)
    success = install_requirements()
    
    # في حالة الفشل، جرب التثبيت الفردي
    if not success:
        print("\n⚠️ فشل تثبيت requirements.txt، محاولة التثبيت الفردي...")
        print("─" * 40)
        success = install_individual_packages()
    
    # التحقق من التثبيت
    print("\n" + "─" * 40)
    verification_success = verify_installation()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if success and verification_success:
        print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
        print("💡 يمكنك الآن تشغيل البرنامج: python main.py")
        return True
    else:
        print("❌ فشل في تثبيت بعض المتطلبات")
        print("💡 جرب تشغيل: python check_system.py للمزيد من التفاصيل")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
