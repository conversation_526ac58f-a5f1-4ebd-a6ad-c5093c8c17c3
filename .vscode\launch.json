{"version": "0.2.0", "configurations": [{"name": "Python: Main Program", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true}, {"name": "Python: Test File", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_all_systems.py", "console": "integratedTerminal", "justMyCode": false}]}