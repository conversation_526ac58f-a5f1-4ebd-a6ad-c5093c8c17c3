@echo off
chcp 65001 >nul
title برنامج المحاسبة - Accounting Program

echo ========================================
echo 🚀 مشغل برنامج المحاسبة
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo 💡 يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    pause
    exit /b 1
)

if not exist "run_safe.py" (
    echo ❌ ملف run_safe.py غير موجود
    echo 💡 تشغيل البرنامج مباشرة...
    python main.py
    pause
    exit /b 0
)

REM تشغيل المشغل الآمن
echo 🔄 تشغيل المشغل الآمن...
python run_safe.py

echo.
echo ========================================
echo 🏁 انتهى التشغيل
echo ========================================
pause
