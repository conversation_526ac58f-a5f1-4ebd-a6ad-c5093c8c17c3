{"python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "files.encoding": "utf8", "files.associations": {"*.py": "python"}, "terminal.integrated.defaultProfile.windows": "PowerShell", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["."]}