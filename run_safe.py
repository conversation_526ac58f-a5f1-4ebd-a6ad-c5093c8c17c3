#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت التشغيل الآمن للبرنامج
يقوم بفحص النظام قبل التشغيل وإصلاح المشاكل تلقائياً
"""

import sys
import os
import subprocess
import time

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🚀 مشغل البرنامج الآمن - Safe Program Launcher")
    print("=" * 70)
    print("📋 هذا المشغل سيقوم بـ:")
    print("   • فحص سلامة النظام")
    print("   • إصلاح المشاكل تلقائياً")
    print("   • تشغيل البرنامج بأمان")
    print("─" * 70)

def run_system_check():
    """تشغيل فحص النظام"""
    print("\n🔍 المرحلة 1: فحص سلامة النظام...")
    try:
        result = subprocess.run([sys.executable, "check_system.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ فحص النظام نجح")
            return True
        else:
            print("❌ فحص النظام فشل")
            print("📄 تفاصيل الخطأ:")
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل فحص النظام: {e}")
        return False

def auto_fix_issues():
    """إصلاح المشاكل تلقائياً"""
    print("\n🔧 المرحلة 2: إصلاح المشاكل تلقائياً...")
    
    # محاولة تثبيت المتطلبات
    print("📦 تثبيت المتطلبات المفقودة...")
    try:
        result = subprocess.run([sys.executable, "install_requirements.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم إصلاح المشاكل بنجاح")
            return True
        else:
            print("⚠️ بعض المشاكل لم يتم إصلاحها")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ خطأ في إصلاح المشاكل: {e}")
        return False

def run_final_check():
    """فحص نهائي قبل التشغيل"""
    print("\n🔍 المرحلة 3: فحص نهائي...")
    return run_system_check()

def launch_program():
    """تشغيل البرنامج الرئيسي"""
    print("\n🚀 المرحلة 4: تشغيل البرنامج...")
    print("─" * 50)
    print("🎯 بدء تشغيل البرنامج الرئيسي...")
    print("⏳ انتظر قليلاً...")
    
    try:
        # تشغيل البرنامج في عملية منفصلة
        process = subprocess.Popen([sys.executable, "main.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8')
        
        # انتظار قصير للتأكد من بدء التشغيل
        time.sleep(3)
        
        # فحص حالة العملية
        if process.poll() is None:
            print("✅ البرنامج يعمل بنجاح!")
            print("💡 يمكنك الآن استخدام البرنامج")
            
            # انتظار انتهاء البرنامج
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                print("\n✅ تم إغلاق البرنامج بنجاح")
            else:
                print(f"\n⚠️ البرنامج أُغلق مع رمز خطأ: {process.returncode}")
                if stderr:
                    print("📄 رسائل الخطأ:")
                    print(stderr)
            
            return True
        else:
            print("❌ فشل في تشغيل البرنامج")
            stdout, stderr = process.communicate()
            if stderr:
                print("📄 رسائل الخطأ:")
                print(stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        return False

def show_manual_instructions():
    """عرض تعليمات التشغيل اليدوي"""
    print("\n" + "🔧 تعليمات التشغيل اليدوي:")
    print("─" * 50)
    print("1️⃣ فحص النظام:")
    print("   python check_system.py")
    print("\n2️⃣ تثبيت المتطلبات:")
    print("   python install_requirements.py")
    print("\n3️⃣ تشغيل البرنامج:")
    print("   python main.py")
    print("\n4️⃣ في حالة المشاكل:")
    print("   python -m pip install -r requirements.txt")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ["main.py", "check_system.py", "install_requirements.py", "requirements.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        print("💡 تأكد من وجود جميع ملفات البرنامج")
        return False
    
    # المرحلة 1: فحص النظام
    if not run_system_check():
        print("\n⚠️ تم اكتشاف مشاكل في النظام")
        
        # المرحلة 2: إصلاح تلقائي
        if auto_fix_issues():
            # المرحلة 3: فحص نهائي
            if not run_final_check():
                print("\n❌ لا يزال هناك مشاكل بعد الإصلاح")
                show_manual_instructions()
                return False
        else:
            print("\n❌ فشل في الإصلاح التلقائي")
            show_manual_instructions()
            return False
    
    # المرحلة 4: تشغيل البرنامج
    success = launch_program()
    
    if success:
        print("\n" + "=" * 70)
        print("🎉 تم تشغيل البرنامج بنجاح!")
        print("=" * 70)
    else:
        print("\n" + "=" * 70)
        print("❌ فشل في تشغيل البرنامج")
        show_manual_instructions()
        print("=" * 70)
    
    return success

if __name__ == "__main__":
    success = main()
    
    # انتظار قبل الإغلاق
    input("\n⏸️ اضغط Enter للخروج...")
    sys.exit(0 if success else 1)
