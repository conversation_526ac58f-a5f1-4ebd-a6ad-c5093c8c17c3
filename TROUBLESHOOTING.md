# 🔧 دليل استكشاف الأخطاء وإصلاحها
## Troubleshooting Guide

---

## 🚀 طرق التشغيل المختلفة

### 1. التشغيل الآمن (مُوصى به)
```bash
# Windows
start_program.bat

# أو
python run_safe.py
```

### 2. التشغيل المباشر
```bash
python main.py
```

### 3. فحص النظام فقط
```bash
python check_system.py
```

---

## ❌ المشاكل الشائعة والحلول

### 1. خطأ "ModuleNotFoundError"
**المشكلة:** مكتبة مفقودة
```
ModuleNotFoundError: No module named 'PyQt5'
```

**الحل:**
```bash
# تثبيت تلقائي لجميع المتطلبات
python install_requirements.py

# أو تثبيت يدوي
python -m pip install PyQt5 SQLAlchemy cryptography openpyxl reportlab
```

### 2. خطأ "ImportError" في utils
**المشكلة:** دوال مفقودة في utils
```
ImportError: cannot import name 'show_advanced_info' from 'utils'
```

**الحل:** الدوال موجودة الآن في `utils/__init__.py`

### 3. مشاكل قاعدة البيانات
**المشكلة:** خطأ في الاتصال بقاعدة البيانات

**الحل:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
del accounting.db
python main.py
```

### 4. مشاكل التشفير
**المشكلة:** خطأ في تحميل مفتاح التشفير

**الحل:**
```bash
# إعادة تثبيت مكتبة التشفير
python -m pip install --upgrade cryptography
```

### 5. مشاكل واجهة المستخدم
**المشكلة:** النافذة لا تظهر أو تتجمد

**الحل:**
```bash
# إعادة تثبيت PyQt5
python -m pip install --upgrade PyQt5
```

---

## 🔍 أدوات التشخيص

### 1. فحص شامل للنظام
```bash
python check_system.py
```

### 2. اختبار استيراد مكتبة محددة
```bash
python -c "import PyQt5; print('PyQt5 OK')"
python -c "import sqlalchemy; print('SQLAlchemy OK')"
python -c "import cryptography; print('Cryptography OK')"
```

### 3. فحص إصدار Python
```bash
python --version
```

### 4. فحص المكتبات المثبتة
```bash
python -m pip list
```

---

## 🛠️ إصلاح المشاكل المتقدمة

### 1. إعادة تثبيت جميع المتطلبات
```bash
python -m pip uninstall -y PyQt5 SQLAlchemy cryptography openpyxl reportlab
python -m pip install -r requirements.txt
```

### 2. تنظيف cache pip
```bash
python -m pip cache purge
python -m pip install --no-cache-dir -r requirements.txt
```

### 3. إنشاء بيئة افتراضية جديدة
```bash
python -m venv new_venv
new_venv\Scripts\activate
python -m pip install -r requirements.txt
```

### 4. فحص تضارب المكتبات
```bash
python -m pip check
```

---

## 📋 قائمة فحص سريعة

- [ ] Python 3.6+ مثبت
- [ ] جميع الملفات موجودة (main.py, utils/, ui/)
- [ ] المكتبات المطلوبة مثبتة
- [ ] لا توجد أخطاء في check_system.py
- [ ] قاعدة البيانات قابلة للوصول
- [ ] مفتاح التشفير صالح

---

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **شغل الفحص الشامل:**
   ```bash
   python check_system.py > system_report.txt
   ```

2. **احفظ رسالة الخطأ كاملة**

3. **تحقق من:**
   - إصدار Python
   - نظام التشغيل
   - المكتبات المثبتة

4. **جرب التشغيل الآمن:**
   ```bash
   python run_safe.py
   ```

---

## 📞 معلومات إضافية

- **الملفات المطلوبة:** main.py, utils/, ui/, system_initializer.py
- **المكتبات الأساسية:** PyQt5, SQLAlchemy, cryptography
- **المكتبات الإضافية:** openpyxl, reportlab, Pillow
- **قاعدة البيانات:** accounting.db (يتم إنشاؤها تلقائياً)

---

*آخر تحديث: 2025-07-28*
