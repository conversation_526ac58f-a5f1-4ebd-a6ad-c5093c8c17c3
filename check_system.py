#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت فحص سلامة النظام المحسن
يتحقق من جميع التبعيات والاستيرادات قبل تشغيل البرنامج
"""

import sys
import os
import importlib.util

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_required_packages():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    required_packages = [
        ('PyQt5', 'واجهة المستخدم الرسومية'),
        ('sqlalchemy', 'قاعدة البيانات'), 
        ('cryptography', 'التشفير والأمان'),
        ('openpyxl', 'ملفات Excel'),
        ('reportlab', 'ملفات PDF'),
        ('PIL', 'معالجة الصور')
    ]
    
    missing_packages = []
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description}) - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_packages)}")
        return False
    
    return True

def check_database_file():
    """فحص ملف قاعدة البيانات"""
    print("\n🔍 فحص قاعدة البيانات...")
    db_file = "accounting.db"
    if os.path.exists(db_file):
        size = os.path.getsize(db_file)
        print(f"✅ ملف قاعدة البيانات موجود: {db_file} ({size} بايت)")
        return True
    else:
        print(f"⚠️ ملف قاعدة البيانات غير موجود: {db_file}")
        print("💡 سيتم إنشاؤه تلقائياً عند أول تشغيل")
        return True

def check_utils_functions():
    """فحص دوال utils"""
    print("\n🔍 فحص دوال utils...")
    try:
        from utils import (
            show_advanced_info,
            show_advanced_error, 
            show_advanced_confirmation,
            show_advanced_warning,
            show_info_message,
            show_error_message
        )
        print("✅ جميع دوال utils متوفرة")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد دوال utils: {e}")
        return False

def check_ui_imports():
    """فحص استيرادات واجهة المستخدم"""
    print("\n🔍 فحص واجهات المستخدم...")
    ui_modules = [
        ('ui.clients', 'قسم العملاء'),
        ('ui.suppliers', 'قسم الموردين'), 
        ('ui.employees', 'قسم الموظفين'),
        ('ui.main_window', 'النافذة الرئيسية')
    ]
    
    failed_imports = []
    for module, description in ui_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module} ({description})")
        except Exception as e:
            print(f"❌ {module} ({description}): {str(e)[:50]}...")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n⚠️ فشل في استيراد: {', '.join(failed_imports)}")
        return False
    
    return True

def check_system_initializer():
    """فحص مُهيئ النظام"""
    print("\n🔍 فحص مُهيئ النظام...")
    try:
        from system_initializer import initialize_advanced_systems
        print("✅ مُهيئ النظام متوفر")
        return True
    except ImportError as e:
        print(f"❌ خطأ في مُهيئ النظام: {e}")
        return False

def check_main_file():
    """فحص الملف الرئيسي"""
    print("\n🔍 فحص الملف الرئيسي...")
    if not os.path.exists("main.py"):
        print("❌ ملف main.py غير موجود")
        return False
    
    try:
        # محاولة استيراد main بدون تشغيله
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "main.py")
        if spec is None:
            print("❌ لا يمكن تحميل main.py")
            return False
        print("✅ ملف main.py صالح للتشغيل")
        return True
    except Exception as e:
        print(f"❌ خطأ في main.py: {e}")
        return False

def suggest_fixes():
    """اقتراح حلول للمشاكل"""
    print("\n" + "🔧 حلول مقترحة:")
    print("─" * 50)
    print("1️⃣ لتثبيت جميع المتطلبات تلقائياً:")
    print("   python install_requirements.py")
    print("\n2️⃣ لتثبيت المكتبات من requirements.txt:")
    print("   python -m pip install -r requirements.txt")
    print("\n3️⃣ لتثبيت مكتبة محددة:")
    print("   python -m pip install [اسم_المكتبة]")
    print("\n4️⃣ لإعادة تثبيت جميع المتطلبات:")
    print("   python -m pip install -r requirements.txt --force-reinstall")
    print("\n5️⃣ في حالة مشاكل pip:")
    print("   python -m pip install --upgrade pip")
    print("\n6️⃣ لفحص مشاكل الاستيراد:")
    print("   python -c \"import [اسم_المكتبة]; print('OK')\"")

def run_quick_test():
    """تشغيل اختبار سريع للبرنامج"""
    print("\n🧪 اختبار سريع للبرنامج...")
    try:
        # محاولة استيراد الوحدات الأساسية
        from PyQt5.QtWidgets import QApplication
        from utils import show_info_message
        print("✅ الاختبار السريع نجح")
        return True
    except Exception as e:
        print(f"❌ فشل الاختبار السريع: {e}")
        return False

def main():
    """الدالة الرئيسية للفحص"""
    print("=" * 60)
    print("🔧 فحص سلامة النظام قبل التشغيل - الإصدار المحسن")
    print("=" * 60)
    
    checks = [
        check_python_version(),
        check_required_packages(),
        check_database_file(),
        check_utils_functions(),
        check_ui_imports(),
        check_system_initializer(),
        check_main_file(),
        run_quick_test()
    ]
    
    passed_checks = sum(checks)
    total_checks = len(checks)
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الفحص: {passed_checks}/{total_checks} فحوصات نجحت")
    
    if all(checks):
        print("🎉 جميع الفحوصات نجحت! النظام جاهز للتشغيل")
        print("💡 يمكنك الآن تشغيل: python main.py")
        return True
    else:
        print("❌ بعض الفحوصات فشلت! يرجى إصلاح المشاكل أولاً")
        suggest_fixes()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
